# PRD: DyberPet AI伴侣对话功能

**版本:** 1.0
**日期:** 2025年7月13日
**作者:** Gemini

---

## 1. 背景与目标

### 1.1 项目背景
DyberPet是一款桌面虚拟宠物应用，其核心价值在于为用户提供陪伴和互动。当前，宠物的互动模式主要基于预设的动画和行为，缺乏动态和个性化的交流能力。项目`docs`目录下的`dialogue_graph`等文件表明，开发者在早期已有构建深度对话系统的规划。

### 1.2 功能目标
本次功能迭代的核心目标是将标准的**AI对话大模型**集成到DyberPet中，实现以下目标：

*   **功能升级:** 将宠物从一个“虚拟宠物”升级为能够进行开放式、自然语言交流的“AI伴侣”。
*   **提升互动体验:** 让用户可以与宠物进行真实、有趣、富有情感的对话，极大地增强产品的互动性和趣味性。
*   **增强个性化:** 通过精心设计的Prompt，赋予每个宠物独特、鲜活的“灵魂”和性格，使其反应不再是千篇一律的脚本。
*   **提高用户粘性:** 创造一个用户愿意花时间与之交流的智能伙伴，从而显著提升用户留存率和使用时长。

## 2. 功能需求详述

### 2.1 核心对话引擎

| 需求ID | 需求描述 | 优先级 |
| :--- | :--- | :--- |
| AI-001 | **AI服务集成:** 系统需支持通过API调用外部AI大模型服务（如OpenAI GPT系列、Google Gemini等）。 | **高** |
| AI-002 | **API密钥管理:** 用户必须在设置中配置自己的API Key，所有API请求的费用由用户承担。 | **高** |
| AI-003 | **Prompt工程:** 系统需内置一套Prompt模板，用于定义宠物的核心角色设定（如“派蒙”的性格、背景、说话风格）。该设定将在每次API请求时附加，以确保AI回复符合宠物个性。 | **高** |
| AI-004 | **上下文记忆:** 对话系统需要支持基本的短期记忆，能够将最近的几轮对话（例如，最近3-5轮）作为上下文发送给AI，以实现更连贯的对话。 | **中** |

### 2.2 用户交互与UI

| 需求ID | 需求描述 | 优先级 |
| :--- | :--- | :--- |
| UI-001 | **对话触发 - 主动:** 在宠物的右键菜单中增加一个“聊天”选项。点击后，弹出一个简洁的文本输入框，供用户输入内容。 | **高** |
| UI-002 | **对话触发 - 被动:** 宠物能够基于特定事件主动发起对话（例如，应用启动、用户长时间无操作等），让互动更自然。 | **中** |
| UI-003 | **对话显示:** 复用项目已有的`bubbleManager.py`气泡框组件，将AI生成的回复文本显示在宠物上方。气泡样式应与现有风格保持一致。 | **高** |
| UI-004 | **输入界面:** 对话输入框应包含一个文本区域和一个“发送”按钮。支持按`Enter`键快速发送。界面应简洁、不打扰。 | **高** |

### 2.3 设置与配置

| 需求ID | 需求描述 | 优先级 |
| :--- | :--- | :--- |
| CFG-001 | **功能总开关:** 在`DyberSettings`面板中，提供一个总开关，允许用户“启用/禁用AI对话功能”。禁用后，所有相关UI入口（如右键菜单）应被隐藏。 | **高** |
| CFG-002 | **API Key配置:** 在设置面板中提供一个安全的文本输入框，用于用户填写和保存自己的API Key。输入框旁应有帮助说明，引导用户获取密钥。 | **高** |
| CFG-003 | **模型选择:** （可选，二期）提供一个下拉菜单，允许用户在支持的模型中进行选择（如GPT-4o, GPT-3.5-Turbo）。 | **低** |

### 2.4 错误处理

| 需求ID | 需求描述 | 优先级 |
| :--- | :--- | :--- |
| ERR-001 | **无效API Key:** 当API返回认证失败时，通过`Notification.py`系统提示用户“API Key无效或已过期，请检查设置”。 | **高** |
| ERR-002 | **网络问题:** 当无法连接到API服务时，在宠物气泡框中显示“网络好像断开了，我暂时没法思考啦~”。 | **高** |
| ERR-003 | **其他API错误:** 对于API返回的其它错误（如速率限制、服务器内部错误），统一显示为“我脑子有点乱，等会儿再聊吧！”。 | **高** |

## 3. 高阶技术方案

1.  **创建AI服务模块:**
    *   在`DyberPet/`目录下新建`ai_service.py`。
    *   该模块将封装所有与AI API的交互逻辑，包括构建请求头、处理请求体、发送HTTP请求以及解析响应。
    *   提供一个核心函数，如`get_ai_response(api_key: str, prompt: str, history: list) -> str`。

2.  **连接UI与服务:**
    *   修改宠物主逻辑文件（如`DyberPet.py`），在处理“聊天”菜单事件时，调用`ai_service.py`中的函数。
    *   获取返回的文本后，将其传递给`bubbleManager.py`实例进行显示。

3.  **配置存储:**
    *   用户的API Key和AI功能开关状态应保存在应用的配置文件中（如`conf.py`或相关json文件）。读取时注意安全处理。

## 4. 成功指标

*   **功能采用率:** >50%的日活跃用户启用了AI对话功能。
*   **互动深度:** 启用功能的用户，平均每日对话交互次数 > 5次。
*   **用户反馈:** 通过社区、GitHub issue等渠道收集到关于新功能的正面评价。

## 5. 未来迭代方向（V2.0）

*   **长期记忆:** 引入向量数据库或本地文件，让宠物能记住关于用户的关键信息（如名字、喜好），并在对话中运用。
*   **语音交互:** 集成语音转文本（STT）和文本转语音（TTS），实现与宠物的语音对话。
*   **Function Calling:** 允许AI调用应用内的其它功能，例如用户说“帮我打开任务列表”，宠物就能直接打开对应的UI界面。
*   **本地模型支持:** 集成对轻量级本地AI模型（如Llama 3 8B）的支持，为用户提供离线、更私密的对话选项。

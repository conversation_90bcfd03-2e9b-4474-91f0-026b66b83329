# AI对话功能 - 开发任务清单 (To-Do List)

本文档根据`PRD_AI_Dialogue.md`创建，旨在为开发者提供一个清晰、有序的任务列表，以确保AI对话功能的顺利实现。

---

### Phase 1: 核心服务搭建 (Backend)

*   [x] **创建模块文件:** 在 `DyberPet/` 目录下新建 `ai_service.py` 文件。
*   [x] **实现API调用:** 在 `ai_service.py` 中，编写核心函数，负责向指定的AI大模型（如OpenAI API）发送HTTP请求。此函数应能处理API Key、请求内容和对话历史。
*   [x] **构建Prompt生成器:** 实现一个函数，用于根据宠物（如“派蒙”）的性格和背景，结合用户的输入，动态生成完整的Prompt。
*   [x] **实现上下文管理:** 为对话逻辑添加短期记忆功能，能够缓存最近的几轮对话，并将其作为上下文加入到下一次的API请求中。

### Phase 2: 用户界面集成 (UI)

*   [x] **添加对话入口:** 修改宠物右键菜单，增加“聊天”选项以触发对话功能。
*   [x] **创建输入界面:** 设计并实现一个简单的对话输入框，包含文本输入区域和“发送”按钮。
*   [x] **连接UI与核心服务:** 将输入界面与`ai_service.py`连接。当用户点击“发送”后，获取输入内容，调用AI服务并等待返回。
*   [x] **显示AI回复:** 调用 `bubbleManager.py` 组件，将AI服务返回的文本内容以气泡框的形式显示在宠物上方。

### Phase 3: 设置与配置

*   [x] **添加功能总开关:** 在 `DyberSettings` 模块中，增加一个UI开关（如QCheckBox），允许用户全局启用或禁用AI对话功能。
*   [x] **添加API Key输入框:** 在 `DyberSettings` 中，增加一个安全的文本输入框（如QLineEdit），供用户填写和保存自己的API Key。
*   [x] **实现配置读写:** 确保应用能够正确地从配置文件中读取/写入AI功能的开关状态和API Key，并在启动时应用这些设置。

### Phase 4: 错误处理与优化

*   [x] **处理无效API Key:** 实现当API返回401或类似认证错误时的逻辑，通过`Notification.py`向用户发送明确的错误提示。
*   [x] **处理网络错误:** 增加try-except块来捕获网络请求中可能出现的异常（如连接超时），并向用户显示友好的提示信息。
*   [x] **处理其他API异常:** 为API可能返回的其他错误（如请求过快、服务器问题）设置通用的错误反馈。
*   [ ] **优化被动对话:** （可选，可延后）实现`UI-002`中描述的被动对话触发逻辑，让交互更加生动。

### Phase 5: 测试与文档

*   [ ] **端到端测试:** 全面测试整个对话流程：从右键菜单打开 -> 输入 -> 发送 -> 等待 -> 显示回复 -> 检查上下文 -> 检查设置项。
*   [ ] **更新用户文档:** 修改`README.md`文件，向用户说明新的AI对话功能、如何开启以及如何配置API Key。
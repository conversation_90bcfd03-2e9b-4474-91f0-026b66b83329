<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1">
<context>
    <name>CharCard</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="885"/>
        <source>Character Info</source>
        <translation>角色信息</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="885"/>
        <source>Mini-Pet Info</source>
        <translation>迷你宠物信息</translation>
    </message>
</context>
<context>
    <name>CharCardWidget</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1028"/>
        <source>Unnamed</source>
        <translation>未命名</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1152"/>
        <source>Go to folder</source>
        <translation>前往文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1154"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1074"/>
        <source>No description.</source>
        <translation>无描述。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1101"/>
        <source>Unknown author</source>
        <translation>未知作者</translation>
    </message>
</context>
<context>
    <name>CharInterface</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="70"/>
        <source>Characters</source>
        <translation>角色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="51"/>
        <source>Characters Management</source>
        <translation>角色管理</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="53"/>
        <source>Collected Characters</source>
        <translation>角色合集</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="58"/>
        <source>Add Characters</source>
        <translation>添加角色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="156"/>
        <source>Switch to </source>
        <translation>切换到 </translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="159"/>
        <source>Might take some time, just wait a moment &lt;3</source>
        <translation>可能加载时间略长，等一下就好啦！&lt;3</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="173"/>
        <source>Loading Character...</source>
        <translation>加载角色...</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="182"/>
        <source>Launched!</source>
        <translation>启动完成！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="177"/>
        <source>Function incomplete</source>
        <translation>功能未完成</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="178"/>
        <source>The function has not been implemented yet.
Currently, you can Go To Folder, delete the whole folder, and restart App.
Sorry for the inconvenience.</source>
        <translation>删除功能尚未完成。
目前，您可以前往角色文件夹，并删除整个文件夹，重启程序。
抱歉带来不便。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="194"/>
        <source>Adding Character</source>
        <translation>添加角色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="195"/>
        <source>You are about to import a character from a local file. Please be aware that it is from third-party sources. We are not responsible for any potential harm or issues that may arise from using this character. Only proceed if you trust the source.</source>
        <translation>您即将从本地文件导入一个来自第三方的角色。请注意，我们不对使用此角色可能引起的任何潜在伤害或问题负责。如果您信任该来源，请继续。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="200"/>
        <source>Please select the character folder</source>
        <translation>请选择角色文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="250"/>
        <source>Adding character completed! It's recommended to restart the App to have all features enabled.</source>
        <translation>添加角色完成！建议重启应用以获得角色的所有相关内容！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="259"/>
        <source>There is already a character with the same name added.</source>
        <translation>已存在相同名称的角色。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="264"/>
        <source>Copying Files</source>
        <translation>正在复制文件</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="264"/>
        <source>Please wait patiently</source>
        <translation>请耐心等待</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="273"/>
        <source>Copying folder failed with unknown reason.</source>
        <translation>因未知原因复制文件夹失败。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="287"/>
        <source>Copy complete!</source>
        <translation>复制完成！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="297"/>
        <source>Adding Failed</source>
        <translation>添加失败</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="298"/>
        <source>Success!</source>
        <translation>成功！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="299"/>
        <source>pet_conf.json broken or not exist!</source>
        <translation>pet_conf.json 文件损坏或不存在！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="300"/>
        <source>act_conf.json broken or not exist</source>
        <translation>act_conf.json 文件损坏或不存在</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="301"/>
        <source>The following actions are missing &quot;images&quot; attribute:</source>
        <translation>以下动作缺少“images”属性：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="302"/>
        <source>The following image files missing:</source>
        <translation>以下图片文件缺失：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="303"/>
        <source>The following default actions missing in pet_conf.json:</source>
        <translation>pet_conf.json 中缺少以下默认动作：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="304"/>
        <source>The following actions called by pet_conf.json are missing from act_conf.json:</source>
        <translation>以下由 pet_conf.json 调用的动作在 act_conf.json 中缺失：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="391"/>
        <source>items_config.json broken or not exist.</source>
        <translation>items_config.json 文件损坏或不存在</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="392"/>
        <source>&apos;image&apos; key missing:</source>
        <translation>&apos;image&apos; 值缺失：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="393"/>
        <source>The following items are missing image files:</source>
        <translation>以下物品缺失图片文件：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="394"/>
        <source>In the following items, &apos;pet_limit&apos; is not a list:</source>
        <translation>在以下物品中，&apos;pet_limit&apos; 不是一个 list：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="405"/>
        <source>Character Management Guide</source>
        <translation>角色管理指南</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="406"/>
        <source>This panel shows all the characters you have so far.
You can switch to a char, or check the character information.

By clicking Add Characters, you can import a new one from the selected folder.
To find new characters, you can check our official collection by clicking the hyperlink button.

For most of time, App can import the character for you automatically. But in any case you want to add it manually:</source>
        <translation>角色管理面板显示你现在所拥有的所有角色。
你可以在此处切换宠物，或者查看有关宠物和作者的详细信息。

点击 添加角色, 你可以从选择的文件夹中导入新的角色。
如果你在寻找新的角色, 你可以点击面板的超链接按钮查看我们收集到的所有角色。

绝大多数情况, 应用可以帮你自动导入新角色。但如果你想要手动导入：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="314"/>
        <source>1. Prepare the character folder containing all files;
2. Copy the folder to App resource folder (you can click &apos;Go to Folder&apos; button);
3. Close App and open again;
4. You will see the character show up here;
5. Click &apos;Launch&apos; to start;
6. If App crushed, it means the character file is problematic, please contact the author for help.</source>
        <translation>1. 准备包含所有文件的角色文件夹；
2. 将文件夹复制到应用程序资源文件夹（您可以点击“前往文件夹”按钮）；
3. 重启应用程序；
4. 您会在此处看到角色；
5. 点击“启动”开始；
6. 如果应用程序崩溃，表示角色文件有问题，请联系作者以获取帮助。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="315"/>
        <source>Go to Folder</source>
        <translation>前往文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="327"/>
        <source>OK</source>
        <translation>确定</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="330"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>CharLine</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="841"/>
        <source>Launch</source>
        <translation>启动</translation>
    </message>
</context>
<context>
    <name>ControlMainWindow</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="45"/>
        <source>Settings</source>
        <translation>基本设置</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="46"/>
        <source>Game Save</source>
        <translation>游戏存档</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="49"/>
        <source>Characters</source>
        <translation>角色管理</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="55"/>
        <source>Item MOD</source>
        <translation>物品模组</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="52"/>
        <source>Mini-Pets</source>
        <translation>迷你宠物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="75"/>
        <source>System</source>
        <translation>系统</translation>
    </message>
</context>
<context>
    <name>DPDialogue</name>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="3125"/>
        <source>Segoe UI</source>
        <translation>黑体</translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="3304"/>
        <source>Back</source>
        <translation>上一步</translation>
    </message>
</context>
<context>
    <name>DPMouseDecor</name>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1628"/>
        <source>Withdraw</source>
        <translation>收回</translation>
    </message>
</context>
<context>
    <name>DPNote</name>
    <message>
        <location filename="../../DyberPet/Notification.py" line="265"/>
        <source>Your pet is starving! (Favor point starts decreasing)</source>
        <translation>宠物要饿死啦！(好感度开始下降)</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Notification.py" line="267"/>
        <source>Your pet is hungry now~ (Favor point stops increasing)</source>
        <translation>宠物现在很饿哦~（好感度停止增加）</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Notification.py" line="273"/>
        <source>Congrats! You have reached the max FV level! Thank you for your companionship all this time!</source>
        <translation>恭喜你！好感度已达上限！感谢这么久以来的陪伴！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Notification.py" line="276"/>
        <source>Favor leveled up:</source>
        <translation>好感度升级至</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Notification.py" line="276"/>
        <source>More features have been unlocked!</source>
        <translation>更多的内容可能已被解锁！</translation>
    </message>
</context>
<context>
    <name>Interaction_worker</name>
    <message>
        <location filename="../../DyberPet/modules.py" line="450"/>
        <source>needs Satiety be larger than</source>
        <translation>需要饱食度大于</translation>
    </message>
</context>
<context>
    <name>ItemCard</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1281"/>
        <source>Item MOD Info</source>
        <translation>物品模组信息</translation>
    </message>
</context>
<context>
    <name>ItemCardWidget</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1421"/>
        <source>Unnamed</source>
        <translation>未命名</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1465"/>
        <source>No description.</source>
        <translation>无描述</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1492"/>
        <source>Unknown author</source>
        <translation>未知作者</translation>
    </message>
</context>
<context>
    <name>ItemInterface</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="54"/>
        <source>Item MOD</source>
        <translation>物品模组</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="56"/>
        <source>Collected Item MOD</source>
        <translation>物品模组合集</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="61"/>
        <source>Add Items</source>
        <translation>添加物品</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="64"/>
        <source>Add Items Manually</source>
        <translation>手动添加物品</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="73"/>
        <source>Items</source>
        <translation>物品模组</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="178"/>
        <source>Function incomplete</source>
        <translation>功能未完成</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="179"/>
        <source>The function has not been implemented yet.
Currently, you can Go To Folder, delete the whole folder, and restart App.
Sorry for the inconvenience.</source>
        <translation>删除功能尚未完成。
目前，您可以前往角色文件夹，并删除整个文件夹，重启程序。
抱歉带来不便。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="328"/>
        <source>Go to Folder</source>
        <translation>前往文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="200"/>
        <source>Adding Item MOD</source>
        <translation>添加物品模组</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="201"/>
        <source>You are about to import a item MOD from a local file. Please be aware that it is from third-party sources. We are not responsible for any potential harm or issues that may arise from using this MOD. Only proceed if you trust the source.</source>
        <translation>您即将从本地文件导入一个来自第三方的模组。请注意，我们不对使用此模组可能引起的任何潜在伤害或问题负责。如果您信任该来源，请继续。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="206"/>
        <source>Please select the MOD folder</source>
        <translation>请选择模组文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="229"/>
        <source>There is already a MOD with the same folder name.</source>
        <translation>已经存在相同文件夹名字的模组</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="271"/>
        <source>Adding item MOD completed! Please restart App to apply MOD.</source>
        <translation>物品模组添加成功！请重启生效模组</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="280"/>
        <source>Copying Files</source>
        <translation>复制文件中</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="280"/>
        <source>Please wait patiently</source>
        <translation>请耐心等待</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="289"/>
        <source>Copy complete!</source>
        <translation>复制完成！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="297"/>
        <source>Copying folder failed with unknown reason.</source>
        <translation>复制文件夹失败</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="312"/>
        <source>Adding Failed</source>
        <translation>添加失败</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="313"/>
        <source>Success!</source>
        <translation>成功</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="314"/>
        <source>items_config.json broken or not exist.</source>
        <translation>items_config.json 文件损坏或不存在</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="315"/>
        <source>&apos;image&apos; key missing:</source>
        <translation>&apos;image&apos; 值缺失：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="316"/>
        <source>The following items are missing image files:</source>
        <translation>以下物品缺失图片文件：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="317"/>
        <source>In the following items, &apos;pet_limit&apos; is not a list:</source>
        <translation>在以下物品中，&apos;pet_limit&apos; 不是一个 list：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="357"/>
        <source>Item MOD Panel Guide</source>
        <translation>物品模组指南</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="358"/>
        <source>This panel shows all the items MODs you have so far.
You can check the MOD detailed information here.

By clicking Add Items, you can import a new MOD from the selected folder.
To find new MODs, you can check our official collection by clicking the hyperlink button.

For most of time, App can import the MOD for you automatically. But in any case you want to add it manually:</source>
        <translation>物品模组面板显示你现在所拥有的所有模组。
你可以在此处查看有关 MOD 和作者的详细信息。

点击 添加物品模组, 你可以从选择的文件夹中导入新的 MOD。
如果你在寻找新的物品, 你可以点击面板的超链接按钮查看我们收集到的所有 MOD。

绝大多数情况, 应用可以帮你自动导入。但如果你想要手动导入：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="327"/>
        <source>1. Prepare the MOD folder containing all files;
2. Copy the folder to App resource folder (you can click &apos;Go to Folder&apos; button);
3. Close App and open again;
4. You will see the MOD show up here;
 *If the MOD not shown or App crushed, it means the MOD file has unexpected error, please contact the author for help.</source>
        <translation>1. 准备包含所有文件的模组文件夹；
2. 将文件夹复制到应用程序资源文件夹（您可以点击“前往文件夹”按钮）；
3. 重启应用程序；
4. 您会在此处看到模组；
* 如果模组未显示或应用程序崩溃，表示模组文件有未预料的问题，请联系作者以获取帮助。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="340"/>
        <source>OK</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="343"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>ItemLine</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1237"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
</context>
<context>
    <name>PetWidget</name>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="833"/>
        <source>More Options</source>
        <translation>更多选项</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="837"/>
        <source>Backpack</source>
        <translation>背包</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="843"/>
        <source>Select Action</source>
        <translation>选择动作</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1596"/>
        <source>Follow Cursor</source>
        <translation>跟随鼠标</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="865"/>
        <source>Call Partner</source>
        <translation>召唤伙伴</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="877"/>
        <source>Tasks</source>
        <translation>计划任务</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="886"/>
        <source>Pomodoro</source>
        <translation>番茄钟</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="892"/>
        <source>Focus</source>
        <translation>专注时间</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="897"/>
        <source>Reminder</source>
        <translation>提醒事项</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="906"/>
        <source>Default Action</source>
        <translation>默认动作</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="922"/>
        <source>Change Character</source>
        <translation>更换角色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1607"/>
        <source>Allow Drop</source>
        <translation>允许掉落</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1603"/>
        <source>Don&apos;t Drop</source>
        <translation>禁用掉落</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="949"/>
        <source>Website</source>
        <translation>快速访问</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1068"/>
        <source> (Fed for </source>
        <translation>（已喂养 </translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1069"/>
        <source> days)</source>
        <translation> 天）</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="916"/>
        <source>Level</source>
        <translation>等级</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1165"/>
        <source>Status: </source>
        <translation>状态：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1158"/>
        <source>System</source>
        <translation>系统</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1057"/>
        <source>Dashboard</source>
        <translation>角色面板</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1158"/>
        <source>Exit</source>
        <translation>退出</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1420"/>
        <source>Satiety</source>
        <translation>饱食度</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1422"/>
        <source>Favorability</source>
        <translation>好感度</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1422"/>
        <source>Favor</source>
        <translation>好感度</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1574"/>
        <source>Stop Follow</source>
        <translation>停止跟随</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1285"/>
        <source>App update available! Please check System - Settings - Check Updates for detail.</source>
        <translation>软件更新可用！请前往 系统 - 基本设置 - 检查更新的发布网页查看。</translation>
    </message>
</context>
<context>
    <name>QAccessory</name>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="371"/>
        <source>Withdraw</source>
        <translation>收回</translation>
    </message>
</context>
<context>
    <name>QuickSaveCard</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="436"/>
        <source>Load In</source>
        <translation>导入</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="440"/>
        <source>Rewrite</source>
        <translation>覆盖</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="444"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="448"/>
        <source>Backtrace</source>
        <translation>回溯</translation>
    </message>
</context>
<context>
    <name>SaveInterface</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="50"/>
        <source>Game Save</source>
        <translation>游戏存档</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="54"/>
        <source>Save Transfer</source>
        <translation>存档传输</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="63"/>
        <source>Choose folder</source>
        <translation>选择文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="63"/>
        <source>Export to</source>
        <translation>导出至</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="71"/>
        <source>Import for</source>
        <translation>为...导入</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="71"/>
        <source>Import from</source>
        <translation>从...导入</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="76"/>
        <source>Please choose the pet, then choose the save folder</source>
        <translation>请先选择宠物，再选择想要导入的文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="254"/>
        <source>All pets</source>
        <translation>所有宠物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="79"/>
        <source>Select pet, then select folder</source>
        <translation>选择宠物，然后选择文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="105"/>
        <source>Quick Save</source>
        <translation>快速存档</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="198"/>
        <source>Choose Export folder</source>
        <translation>导出至</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="214"/>
        <source>Export Succeed!</source>
        <translation>导出成功！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="216"/>
        <source>Export Failed! Please try again.</source>
        <translation>导出失败！请重试。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="225"/>
        <source>Are you sure you want to import another save?</source>
        <translation>您确定要导入另一个存档吗？</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="231"/>
        <source>Choose Save Folder to Import</source>
        <translation>选择要导入的存档文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="248"/>
        <source>File: pet_data.json not found in selected folder!</source>
        <translation>在所选文件夹中未找到文件：pet_data.json！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="258"/>
        <source>File: pet_data.json is not in compatible format!</source>
        <translation>文件：pet_data.json的格式出错！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="270"/>
        <source>Save imported successfully!</source>
        <translation>存档导入成功！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="273"/>
        <source>Failed to import save!</source>
        <translation>存档导入失败！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="283"/>
        <source>Name of the Save</source>
        <translation>存档名称</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="440"/>
        <source>OK</source>
        <translation>确定</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="441"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="321"/>
        <source>Save Succeed!</source>
        <translation>保存成功！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="323"/>
        <source>Save Failed! Please try again.</source>
        <translation>保存失败！请重试。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="434"/>
        <source>Updating Save card failed!</source>
        <translation>更新存档槽失败！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="407"/>
        <source>Error: Save folder in bad format!</source>
        <translation>错误：存档文件夹格式错误！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="365"/>
        <source>Are you sure you want to delete the save?</source>
        <translation>您确定要删除此存档吗？</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="375"/>
        <source>Deletion Succeed!</source>
        <translation>删除成功！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="377"/>
        <source>Error: Deletion Failed!</source>
        <translation>错误：删除失败！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="390"/>
        <source>Are you sure you want to backtrace the save slot?</source>
        <translation>您确定要回溯存档槽吗？</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="412"/>
        <source>Save backtraced successfully!</source>
        <translation>存档槽回溯成功！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="414"/>
        <source>Error: Deleting current save Failed!</source>
        <translation>错误：删除当前存档失败！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="226"/>
        <source>• Make sure you have exported the current save, in case an error happened
• Currently, only save will be imported, note and settings won't be influenced
• Only selected character save will be modified</source>
        <translation>• 请确保您已经导出当前存档，以免导入中发生不可预计的错误
• 目前，只有存档数据会被导入，备忘录和系统设置不受影响
• 只有被选择的角色数据会被导入</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="366"/>
        <source>All history saves in this slot will be deleted, use carefully</source>
        <translation>该存档槽所有历史记录都将被删除，请谨慎使用。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="391"/>
        <source>It will delete the current save, and backtrace to the last one in this slot.</source>
        <translation>当前存档将被删除，回溯至本槽位上一个存档。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="346"/>
        <source>Load in the save?</source>
        <translation>导入存档？</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="347"/>
        <source>Pet save data will be overwritten.</source>
        <translation>角色存档将被覆盖</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="447"/>
        <source>Game Save Guide</source>
        <translation>存档面板指南</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="448"/>
        <source>Game Save Panel is the place you can Save and Load data.

From top to bottom, there are 3 functions:
⏺ Export Data
    - You can make a copy of the data to selected path
    - It contains character, settings, and task-related data

⏺ Import Data
    - You can import character data from the selected folder
    - Choose import for all character or only one specific character
    - Currently, only status and items data can be imported
    - If you want to import other data like customized action, settings, you need to manually copy the files to the App data folder

⏺ Quick Save
    - Quick save frees you from selecting folders, and has more functions
    - It has 6 Slots, and their are 4 operations
    - Load In: Load in the Slot data for this character (! cannot undo, so be careful)
    - Rewrite: Save current character's data in this slot
    - Backtrace: Go back to the previous save data in this slot. Usefull when you accidentally over-write the save. But it also means you will lose the current save
    - Delete: Empty ALL save data in this slot (current one, and all previous ones)</source>
        <translation>在存档面板，你可以方便的导出、载入应用数据。

从上到下，存档面板一共有三个功能:
⏺ 导出数据
    - 你可以把数据导出至指定文件夹
    - 导出的文件夹包含 角色、设置、和每日任务的数据

⏺ 载入存档
    - 你可以给角色载入指定文件夹内的存档信息
    - 你可以选择是给所有角色载入、还是只给指定角色载入
    - 目前，只有角色状态、物品、金币信息可以被载入
    - 如果你想载入自定义动作、设置等数据文件, 你需要手动拷贝到应用的 data 文件夹下

⏺ 快速存档
    - 快速存档不需要选择文件夹，且拥有更多功能
    - 这里一共有 6 个存档槽, 4 项功能
    - 导入: 给当前槽位角色 导入 当前槽位的数据 (! 无法撤回)
    - 覆盖: 给当前槽位写入当前角色的实时数据，旧的存档被覆盖
    - 回溯: 将槽位数据回溯至上一个存档数据. 当不小心覆盖了存档时，可以使用这个功能. 但这也意味着你将失去现在的存档数据
    - 删除: 清空整个槽位 (现在的、和之前所有的存档)</translation>
    </message>
</context>
<context>
    <name>Scheduler_worker</name>
    <message>
        <location filename="../../DyberPet/modules.py" line="724"/>
        <source>*Setting config file broken. Setting is re-initialized.</source>
        <translation>系统设置文件损坏，已重置。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="729"/>
        <source>*Game save file broken. Data is re-initialized.
Please load previous saved data to recover.</source>
        <translation>存档文件损坏，已重置。请导入保存过的存档。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="760"/>
        <source>Good Morning!</source>
        <translation>早上好！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="764"/>
        <source>Good Afternoon!</source>
        <translation>下午好！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="766"/>
        <source>Good Evening!</source>
        <translation>晚上好！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="770"/>
        <source>Time to sleep!</source>
        <translation>该睡觉啦！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="719"/>
        <source>Pomodoro</source>
        <translation>番茄钟</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="720"/>
        <source>The new Pomodoro has started! Let's go!</source>
        <translation>新的番茄钟开始了！加油！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="721"/>
        <source> Pomodoros have been set! Let's dive in!</source>
        <translation> 个番茄钟设定完毕！让我们开始吧！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="722"/>
        <source>Ding ding~ Pomodoro finished! Time for a 5-minute break!</source>
        <translation>叮叮~ 本轮番茄钟完成啦！休息5分钟吧！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="723"/>
        <source>Ding ding~ All Pomodoros completed! Great job!</source>
        <translation>叮叮~ 所有番茄钟都完成啦！好欸！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="724"/>
        <source>Your Pomodoros have all been canceled!</source>
        <translation>番茄钟已全部取消！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="726"/>
        <source>Focus Session</source>
        <translation>专注时段</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="727"/>
        <source>Your focus session has started!</source>
        <translation>你的专注时段开始啦！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="728"/>
        <source>Your focus session has completed!</source>
        <translation>你的专注时段结束啦！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="729"/>
        <source>Your focus session has been canceled!</source>
        <translation>你的专注时段取消啦！</translation>
    </message>
</context>
<context>
    <name>SettingInterface</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="49"/>
        <source>Settings</source>
        <translation>基本设置</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="72"/>
        <source>Mode</source>
        <translation>模式</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="74"/>
        <source>Always-On-Top</source>
        <translation>置顶宠物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="74"/>
        <source>Pet will be displayed on top of the other Apps</source>
        <translation>宠物将始终显示在其他应用程序的上方</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="86"/>
        <source>Allow Drop</source>
        <translation>允许掉落</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="86"/>
        <source>When mouse released, pet falls to the ground (on) / stays at the site (off)</source>
        <translation>当鼠标释放时，宠物会掉落到地面(开) / 停留在原地(关)</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="84"/>
        <source>Auto-Lock</source>
        <translation>自动锁定</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="85"/>
        <source>When screen is locked, HP and FV will be locked too (currently only works in Windows)</source>
        <translation>当屏幕锁定时，饱食度和好感度都会停止变化 (目前仅 Windows 可用)</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="100"/>
        <source>Interaction</source>
        <translation>交互参数</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="101"/>
        <source>Gravity</source>
        <translation>重力</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="101"/>
        <source>Pet falling down acceleration</source>
        <translation>宠物掉落加速度</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="112"/>
        <source>Drag Speed</source>
        <translation>拖拽速度</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="112"/>
        <source>Mouse speed factor</source>
        <translation>鼠标速度系数</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="106"/>
        <source>Notification</source>
        <translation>通知</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="125"/>
        <source>Volumn</source>
        <translation>音量</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="119"/>
        <source>Pop-up Toaster</source>
        <translation>弹出通知栏</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="120"/>
        <source>When turned on, notification will pop-up at the bottom right corner</source>
        <translation>开启时，通知栏会在屏幕右下角弹出</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="147"/>
        <source>Dialogue Bubble</source>
        <translation>对话气泡</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="148"/>
        <source>When turned on, various kinds of bubbles will pop-up above the pet</source>
        <translation>开启时，各种类型的对话气泡会随机出现在桌宠上方</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="125"/>
        <source>Volumn of notification and pet</source>
        <translation>通知和语音的音量</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="136"/>
        <source>Personalization</source>
        <translation>个性化</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="137"/>
        <source>Pet Scale</source>
        <translation>宠物大小</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="137"/>
        <source>Adjust size of the pet</source>
        <translation>调整宠物的大小</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="148"/>
        <source>Default Pet</source>
        <translation>默认宠物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="148"/>
        <source>Pet to show everytime App starts</source>
        <translation>应用启动时显示的宠物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="162"/>
        <source>Language/语言</source>
        <translation>语言/Language</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="163"/>
        <source>Set your preferred language for UI</source>
        <translation>为UI设置您偏好的语言</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="173"/>
        <source>About</source>
        <translation>关于</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="174"/>
        <source>Check Updates</source>
        <translation>检查更新</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="174"/>
        <source>Release Website</source>
        <translation>发布网页</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="174"/>
        <source>New version available</source>
        <translation>有新版本可用</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="174"/>
        <source>Already the latest</source>
        <translation>已是最新版本</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="174"/>
        <source>Failed to check updates. Please check the website.</source>
        <translation>检查更新失败。请自行查看发布页面。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="182"/>
        <source>Issue Page</source>
        <translation>问题页面</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="182"/>
        <source>Help &amp; Issue</source>
        <translation>帮助 &amp; 问题</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="182"/>
        <source>Post your issue or question on our GitHub Issue, or contact us on BiliBili</source>
        <translation>在我们的GitHub问题页面上发布您的问题，或在BiliBili上联系我们</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="190"/>
        <source>Developer Document</source>
        <translation>开发者文档</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="190"/>
        <source>Re-development</source>
        <translation>二次开发</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="190"/>
        <source>If you want to develop your own pet/item/actions... Check here</source>
        <translation>如果您想开发自己的宠物/物品/动作...请在这里查看</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="297"/>
        <source>Configuration takes effect after restart
此设置在重启后生效</source>
        <translation>Configuration takes effect after restart
此设置在重启后生效</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="173"/>
        <source>Theme color</source>
        <translation>主题色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="174"/>
        <source>Change the theme color of you application</source>
        <translation>调整你的应用的主题色</translation>
    </message>
</context>
<context>
    <name>SwitchSettingCard</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="72"/>
        <source>On</source>
        <translation>开</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="72"/>
        <source>Off</source>
        <translation>关</translation>
    </message>
</context>
<context>
    <name>CustomColorSettingCard</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="325"/>
        <source>Default color</source>
        <translation>默认颜色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="327"/>
        <source>Custom color</source>
        <translation>自定义颜色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="399"/>
        <source>Choose color</source>
        <translation>选择颜色</translation>
    </message>
</context>
<context>
    <name>ColorDialog</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="398"/>
        <source>OK</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="398"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="398"/>
        <source>Edit Color</source>
        <translation>编辑颜色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="398"/>
        <source>Red</source>
        <translation>红色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="398"/>
        <source>Blue</source>
        <translation>蓝色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="398"/>
        <source>Green</source>
        <translation>绿色</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="398"/>
        <source>Opacity</source>
        <translation>透明度</translation>
    </message>
</context>
<context>
    <name>SubPet</name>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1261"/>
        <source>Pet Scale</source>
        <translation>宠物大小</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1113"/>
        <source>Select Action</source>
        <translation>选择动作</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1157"/>
        <source>Allow Drop</source>
        <translation>允许掉落</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1153"/>
        <source>Don&apos;t Drop</source>
        <translation>禁用掉落</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1330"/>
        <source>Following Off</source>
        <translation>跟随关闭</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1335"/>
        <source>Following On</source>
        <translation>跟随开启</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1144"/>
        <source>Exit</source>
        <translation>退出</translation>
    </message>
</context>
<context>
    <name>Ui_SaveNameDialog</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_base.py" line="37"/>
        <source>OK</source>
        <translation>确定</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_base.py" line="38"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>others</name>
    <message>
        <source>Starving</source>
        <translation>饿昏</translation>
    </message>
    <message>
        <source>Hungry</source>
        <translation>饥饿</translation>
    </message>
    <message>
        <source>Normal</source>
        <translation>正常</translation>
    </message>
    <message>
        <source>Energetic</source>
        <translation>活力</translation>
    </message>
    <message>
        <source>Satiety</source>
        <translation>饱食度</translation>
    </message>
    <message>
        <source>Favorability</source>
        <translation>好感度</translation>
    </message>
</context>
<context>
    <name>DashboardMainWindow</name>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="54"/>
        <source>Status</source>
        <translation>角色状态</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="57"/>
        <source>Backpack</source>
        <translation>角色背包</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="60"/>
        <source>Shop</source>
        <translation>商店</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="63"/>
        <source>Daily Tasks</source>
        <translation>日常任务</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="73"/>
        <source>Dashboard</source>
        <translation>角色面板</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="69"/>
        <source>Animation</source>
        <translation>动画管理</translation>
    </message>
</context>
<context>
    <name>EmptyTaskCard</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2953"/>
        <source>Add New Task</source>
        <translation>添加新任务</translation>
    </message>
</context>
<context>
    <name>FVWidget</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="435"/>
        <source>Favor</source>
        <translation>好感度</translation>
    </message>
</context>
<context>
    <name>FocusPanel</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1963"/>
        <source>Focus Session</source>
        <translation>专注时间</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2201"/>
        <source>Lauch Focus</source>
        <translation>开始专注</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2002"/>
        <source>Break by Pomodoro</source>
        <translation>使用番茄钟</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2083"/>
        <source>You will not have break time.</source>
        <translation>中途将不会有休息时间。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2023"/>
        <source>Start</source>
        <translation>开始</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2029"/>
        <source>Cancel</source>
        <translation>停止</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2059"/>
        <source>Usage Help</source>
        <translation>使用帮助</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2059"/>
        <source>Please set up a period to focus on the work/study.

Once this focus task is done, you will get coin rewarded.

Even if you stopped the clock in the middle, you will still get rewarded accordingly.

Choose &apos;Break by Pomodoro&apos; will adjust the time to fit closest number of pomodoro.
Everytime you finish a 25min Pomodoro, you get coin rewarded</source>
        <translation>请设置一个工作/学习的专注时间。

专注任务完成后，你将获得金币奖励。

即使你在中途终止，你仍然会得到相应完成部分的奖励。

选择“使用番茄钟”将调整时间，以适应最接近的番茄时间数量。
每当你完成一个25分钟的番茄钟，你就会获得金币奖励</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2080"/>
        <source>You will take a 5-minute break every 25 minutes.</source>
        <translation>每专注 25 分钟你将休息 5 分钟。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2120"/>
        <source>Pomodoro Time Remaining</source>
        <translation>番茄钟剩余</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2142"/>
        <source>Focus Time Remaining</source>
        <translation>专注时间剩余</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2239"/>
        <source>Focus task reward:</source>
        <translation>专注任务奖励：</translation>
    </message>
</context>
<context>
    <name>HPWidget</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="357"/>
        <source>Satiety</source>
        <translation>饱食度</translation>
    </message>
</context>
<context>
    <name>ProgressPanel</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2292"/>
        <source>Daily Goal</source>
        <translation>每日目标</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2322"/>
        <source>Yesterday</source>
        <translation>昨日</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2491"/>
        <source>Minutes</source>
        <translation>分钟</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2347"/>
        <source>Progress</source>
        <translation>今日进度</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2375"/>
        <source>Daily Goal: 180 Minutes</source>
        <translation>每日目标：180 分钟</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2396"/>
        <source>Completed</source>
        <translation>连续达标</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2402"/>
        <source>Days in a row</source>
        <translation>天</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2419"/>
        <source>Set The Goal</source>
        <translation>设定目标</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2420"/>
        <source>Upon reaching your set time, you'll be rewarded with coins. Earn extra rewards by completing certain streaks of consecutive days!</source>
        <translation>达到设定的时间后，将获得金币奖励。通过每日连续达标，可赚取越来越多的额外奖励！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2493"/>
        <source>Daily Goal:</source>
        <translation>每日目标：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2535"/>
        <source>Daily Goal Completed:</source>
        <translation>每日目标已完成：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2539"/>
        <source>days in a row reward:</source>
        <translation>天连续达标奖励：</translation>
    </message>
</context>
<context>
    <name>ShopItemWidget</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1347"/>
        <source>Owned</source>
        <translation>背包 </translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1350"/>
        <source>Favor Req</source>
        <translation>好感度要求 </translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1353"/>
        <source>Other Chars Only</source>
        <translation>其他角色限定</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1388"/>
        <source>Sell</source>
        <translation>出售</translation>
    </message>
</context>
<context>
    <name>ShopMessageBox</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1781"/>
        <source>Buy</source>
        <translation>购买</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1783"/>
        <source>Sell</source>
        <translation>出售</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1795"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>ShopView</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1558"/>
        <source>Type</source>
        <translation>物品类型</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1561"/>
        <source>MOD</source>
        <translation>物品模组</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1504"/>
        <source>Food</source>
        <translation>食物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1505"/>
        <source>Collection</source>
        <translation>收藏品</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1507"/>
        <source>Pet</source>
        <translation>宠物</translation>
    </message>
</context>
<context>
    <name>StatusCard</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="281"/>
        <source> (Fed for </source>
        <translation>（已喂养 </translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="282"/>
        <source> days)</source>
        <translation> 天）</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="302"/>
        <source>Level</source>
        <translation>等级</translation>
    </message>
</context>
<context>
    <name>TaskPanel</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2675"/>
        <source>To-do Tasks</source>
        <translation>待办事项</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2692"/>
        <source>Completed </source>
        <translation>已完成 </translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2696"/>
        <source> tasks</source>
        <translation> 项任务</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2733"/>
        <source>On-Going</source>
        <translation>进行中</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2745"/>
        <source>Completed</source>
        <translation>已完成</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2844"/>
        <source>Task completed! </source>
        <translation>任务完成！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2847"/>
        <source>You completed another 5 tasks! </source>
        <translation>又完成了5项任务！</translation>
    </message>
</context>
<context>
    <name>backpackInterface</name>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="68"/>
        <source>Backpack</source>
        <translation>角色背包</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="267"/>
        <source>Use</source>
        <translation>使用</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="223"/>
        <source>Backpack Guide</source>
        <translation>背包指南</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="224"/>
        <source>Backpack keeps all the items pet got.

There are in total 3 tabs and the coins display:
    - Consumable items (food, etc.)
    - Collections (Dialogue, etc.)
    - Subpet
(All tabs have infinite volume.)

📌Items have different effects, such as adding HP. Some of them also have Buff effects. Please position your cursor over the item to see details.

📌Auto-Feed
If there is any item in the first cell of the consumable item tab, this item will be automatically fed to the pet when the HP is lower than 60.</source>
        <translation>背包内存放角色获得的所有物品。

共有3个标签页和金币栏：
    - 消耗品（食物等）
    - 收藏品（对话等）
    - 角色的宠物
（背包的容量无限）

📌物品有不同的效果，如增加饱食度。其中一些还有Buff效果。
请将鼠标悬停在物品上以查看详情。

📌自动喂食
放置在消耗品栏第一格的物品会被用作自动喂食物品
当桌宠饱食度低于 60 时，将触发自动喂食</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="240"/>
        <source>OK</source>
        <translation>确定</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="243"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="267"/>
        <source>Withdraw</source>
        <translation>收回</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="295"/>
        <source>Randomly dropped</source>
        <translation>随机掉落了</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="315"/>
        <source>Dyber Coin</source>
        <translation>啵币</translation>
    </message>
</context>
<context>
    <name>coinWidget</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="734"/>
        <source>Dyber Coin</source>
        <translation>啵币</translation>
    </message>
</context>
<context>
    <name>shopInterface</name>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="53"/>
        <source>Shop</source>
        <translation>商店</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="72"/>
        <source>Filter</source>
        <translation>筛选</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="100"/>
        <source>Type</source>
        <translation>物品类型</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="100"/>
        <source>Food</source>
        <translation>食物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="100"/>
        <source>Collection</source>
        <translation>收藏品</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="100"/>
        <source>Pet</source>
        <translation>宠物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="103"/>
        <source>MOD</source>
        <translation>物品模组</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="110"/>
        <source>Search by name, MOD...</source>
        <translation>按名称、模组搜索...</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="199"/>
        <source>Shop Guide</source>
        <translation>商店指南</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="200"/>
        <source>You can buy items with Coins in the Shop.

Items can be searched by name, or filtered by the types.

You can either buy an item or sell the item you own. When selling, item will be depreciated.

Items will be unlocked when the requirements like Favor level have been met.

Please position your cursor over the item image to see details.</source>
        <translation>商店中可以使用金币来购买物品。

你可以用搜索栏 或按照类别来查找想要的物品。

物品可以购买或出售，出售时物品会折价。

物品会随要求被满足（如好感度）而解锁。

请将鼠标悬停在物品上以查看详情。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="208"/>
        <source>OK</source>
        <translation>确定</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="211"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="274"/>
        <source>One Char can have only one </source>
        <translation>一个角色只能拥有一个 </translation>
    </message>
</context>
<context>
    <name>statusInterface</name>
    <message>
        <location filename="../../DyberPet/Dashboard/statusUI.py" line="55"/>
        <source>Status</source>
        <translation>角色状态</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/statusUI.py" line="58"/>
        <source>Status Log</source>
        <translation>状态日志</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/statusUI.py" line="58"/>
        <source>Status Guide</source>
        <translation>状态面板指南</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/statusUI.py" line="64"/>
        <source>User Name</source>
        <translation>用户昵称</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/statusUI.py" line="58"/>
        <source>Status Panel is about character status (ofc).

From top to bottom, there are 3 widgets:
⏺ Character Status

⏺ Buff Status
    - The widget shows any Buff the character currently has.
    - Character can get buffed by using a certain item, or take on a certain pet.

⏺ Notification Log
    - Don't worry if you missed any notification, all the notes will be saved here.
    ⚠️ But once you close the App, notes will be gone.</source>
        <translation>状态面板是有关角色状态的面板（废话）
        
从上到下，一共有 3 个组件：

⏺ 角色组件

⏺ Buff 状态
    - 这个组件会显示角色目前拥有的 Buff
    - 可以通过使用特定物品、或带上特定宠物，来获得 Buff 效果

⏺ 通知日志
    - 如果你不小心错过了一条消息，没关系，所有的通知都会记录在这里
    ⚠️ 但是关闭程序后，通知就消失了哦</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/statusUI.py" line="201"/>
        <source>OK</source>
        <translation>确定</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/statusUI.py" line="204"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>taskInterface</name>
    <message>
        <location filename="../../DyberPet/Dashboard/taskUI.py" line="34"/>
        <source>Daily Tasks</source>
        <translation>日常任务</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/taskUI.py" line="109"/>
        <source>Daily Task Guide</source>
        <translation>日常任务指南</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/taskUI.py" line="110"/>
        <source>Daily Task Panel is the place you finish task to get Coin reward.

From top to bottom, there are 3 widgets:
⏺ Focus Session
    - Focus on your work/study for a certain amount of time to get rewards.
    - You can either set a period or use Pomodoro.

⏺ Daily Goal
    - It records your daily progress towards goal.
    - You can set your own goal from the top right corner.
    - The longer your goal is, the more coins you got when you reach the daily goal.
    - Complete numbers of days in a row will get you extra coins reward.

⏺ To-do Tasks
    - Write done any to-dos you have.
    - Once finished, you will get coin reward.
    - Completing every 5 tasks will give you extra reward.</source>
        <translation>完成各种日常任务以获得金币奖励

从上到下，一共有 3 个组件：
⏺ 专注时间
    - 专注工作/学习一段时间来获取奖励
    - 你可以设定一个专注时间段，或者使用番茄钟进行划分

⏺ 每日目标
    - 这里记录了你的每日目标进度
    - 你可以从右上角设定你的每日专注时间目标
    - 目标时长越长，完成后获得的奖励越丰厚
    - 连续完成几日可获得额外的奖励

⏺ 待办事项
    - 你可以在这里记下待办的事情
    - 每完成一个待办事项，都会获得奖励
    - 每完成五个任务，都会获得额外奖励</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/taskUI.py" line="134"/>
        <source>OK</source>
        <translation>确定</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/taskUI.py" line="137"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>ActDesignWindow</name>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="87"/>
        <source>Animation Design</source>
        <translation>动画设计</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="149"/>
        <source>Select an action</source>
        <translation>选择动作</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="156"/>
        <source>Start:</source>
        <translation>起始：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="165"/>
        <source>End:</source>
        <translation>结束：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="174"/>
        <source>Repeat:</source>
        <translation>重复：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="183"/>
        <source>Add</source>
        <translation>添加</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="185"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="223"/>
        <source>Add New</source>
        <translation>新动作</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="255"/>
        <source>New Action Name</source>
        <translation>新动作名称</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="936"/>
        <source>HP Level:</source>
        <translation>饱食度要求：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="288"/>
        <source>Create</source>
        <translation>创建</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="383"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="382"/>
        <source>Confirm</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="521"/>
        <source>Delete?</source>
        <translation>删除？</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="522"/>
        <source>Do you want to delete this single design?
You won't be able to recover after confirming.</source>
        <translation>你确定要删除这单个动作设计吗？
确认后将无法恢复。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="598"/>
        <source>Empty Design</source>
        <translation>无设计</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="599"/>
        <source>You need to add at least one animation to create new design.</source>
        <translation>你需要添加至少一个动作来创建新的动画。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="605"/>
        <source>Name Not Valid</source>
        <translation>名称无效</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="606"/>
        <source>The new name cannot be empty or the same as existing ones.</source>
        <translation>新名称不能为空或与现有名称相同。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="612"/>
        <source>Confirm to create</source>
        <translation>确认创建</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animDesignUI.py" line="613"/>
        <source>After confirmation, designed animation will be added to list</source>
        <translation>确认后，设计的动画将被添加到列表中</translation>
    </message>
</context>
<context>
    <name>ActionCard</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2110"/>
        <source>Action Locked</source>
        <translation>动作未解锁</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2116"/>
        <source>Special action</source>
        <translation>特殊动作</translation>
    </message>
</context>
<context>
    <name>AnimationGroup</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1882"/>
        <source>Action List</source>
        <translation>动作列表</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1893"/>
        <source>Playlist</source>
        <translation>播放列表</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1913"/>
        <source>Customized</source>
        <translation>自定义动作</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1927"/>
        <source>Add New Animation</source>
        <translation>添加新动画</translation>
    </message>
</context>
<context>
    <name>animationInterface</name>
    <message>
        <location filename="../../DyberPet/Dashboard/animationUI.py" line="43"/>
        <source>Animation</source>
        <translation>动画管理</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animationUI.py" line="161"/>
        <source>Delete?</source>
        <translation>删除？</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animationUI.py" line="162"/>
        <source>Do you want to delete this customized animation?
You won&apos;t be able to recover after confirming.</source>
        <translation>您确定要删除这个自定义动画吗？
确认后将无法恢复。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animationUI.py" line="180"/>
        <source>Confirm</source>
        <translation>确认</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animationUI.py" line="181"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animationUI.py" line="202"/>
        <source>Animation Panel Guide</source>
        <translation>动画面板指南</translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animationUI.py" line="203"/>
        <source>In Animation Panel, you can 
⏺ select an action to play
⏺ decide the random playlist by selecting the checkbox
⏺ create customized animation by clicking 'Add New Animation'

📌About the random playlist:
The character will randomly do some action when not being interacted with
At different hunger level, the behavior will be different
If only one action is selected from the list, it will only play the one selected

📌About the customized animation:
Click 'Add New Animation' and the design window will pop up
Select an animation, define your new start, end, and repetition
Click 'Add' to save the single design!
You can repeat this to add more animation in your design
Once done, give the design a name, and click 'Create' to complete</source>
        <translation>在动画面板中，你可以
⏺ 选择播放动作
⏺ 勾选以调整随机播放列表
⏺ 点击 添加新动画 来创建自定义动画

📌关于随机播放列表:
没有交互的时候，角色会自己随机做出动作
在不同的饱食度等级下，角色的行为会有所不同
如果列表中只有一个动作，那么系统会默认只播放这一个动画

📌自定义动画操作:
点击 添加新动画 后会有设计窗口弹出
从下拉菜单选择一个动作, 定义起始、结束、重复次数
点击 添加 来保存这一个动作的设计
你可以添加新的动作设计，把它们按顺序连接起来
完成后，给自定义动作命名, 然后点击 创建 即可完成</translation>
    </message>
</context>
<context>
    <name>PetInterface</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="46"/>
        <source>Mini-Pet Management</source>
        <translation>迷你宠物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="53"/>
        <source>Add Mini-Pets</source>
        <translation>添加迷你宠物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="50"/>
        <source>Collected Mini-Pets</source>
        <translation>迷你宠物合集</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="55"/>
        <source>Add Manually</source>
        <translation>手动添加</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="66"/>
        <source>Mini-Pets</source>
        <translation>宠物列表</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="165"/>
        <source>Function incomplete</source>
        <translation>功能未完成</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="166"/>
        <source>The function has not been implemented yet.
Currently, you can Go To Folder, delete the pet's folder, and restart App.
Sorry for the inconvenience.</source>
        <translation>删除功能尚未完成。
目前，您可以前往宠物文件夹，并删除整个文件夹，重启程序。
抱歉带来不便。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="171"/>
        <source>Go to Folder</source>
        <translation>前往文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="195"/>
        <source>Adding Mini-Pet</source>
        <translation>添加迷你宠物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="196"/>
        <source>You are about to import a Mini-Pet from a local file. Please be aware that it is from third-party sources. We are not responsible for any potential harm or issues that may arise from using this mini-pet. Only proceed if you trust the source.</source>
        <translation>您即将从本地文件导入一个来自第三方的宠物。请注意，我们不对使用此宠物可能引起的任何潜在伤害或问题负责。如果您信任该来源，请继续。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="202"/>
        <source>Please select the pet folder</source>
        <translation>请选择宠物文件夹</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="226"/>
        <source>There is already a Mini-Pet with the same name added.</source>
        <translation>已经存在相同文件夹名字的迷你宠物</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="268"/>
        <source>Adding Mini-Pet completed! You need to restart the App to have the Mini-Pet enabled.</source>
        <translation>迷你宠物添加完成！请重启应用以加载新的宠物。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="278"/>
        <source>Copying Files</source>
        <translation>正在复制文件</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="278"/>
        <source>Please wait patiently</source>
        <translation>请耐心等待</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="294"/>
        <source>Copying folder failed with unknown reason.</source>
        <translation>因未知原因复制文件夹失败。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="287"/>
        <source>Copy complete!</source>
        <translation>复制完成！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="308"/>
        <source>Adding Failed</source>
        <translation>添加失败</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="309"/>
        <source>Success!</source>
        <translation>成功！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="310"/>
        <source>pet_conf.json broken or not exist!</source>
        <translation>pet_conf.json 文件损坏或不存在！</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="311"/>
        <source>act_conf.json broken or not exist</source>
        <translation>act_conf.json 文件损坏或不存在</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="312"/>
        <source>The following actions are missing &quot;images&quot; attribute:</source>
        <translation>以下动作缺少“images”属性：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="313"/>
        <source>The following image files missing:</source>
        <translation>以下图片文件缺失：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="314"/>
        <source>The following default actions missing in pet_conf.json:</source>
        <translation>pet_conf.json 中缺少以下默认动作：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="315"/>
        <source>The following actions called by pet_conf.json are missing from act_conf.json:</source>
        <translation>以下由 pet_conf.json 调用的动作在 act_conf.json 中缺失：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="358"/>
        <source>Mini-Pet Panel Guide</source>
        <translation>迷你宠物指南</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="358"/>
        <source>This panel shows all the Mini-Pets you have so far.
You can check the detailed information here.
                            
Mini-Pets are pets of the characters. They exist as an item in the backpack, and can be purchased, and called.
Some of the pets will always follow your character in its own way.

By clicking Add Mini-Pet, you can import a new pet from the selected folder.
To find new pets, you can check our official collection by clicking the hyperlink button.

For most of time, App can import the pet for you automatically. But in any case you want to add it manually:</source>
        <translation>迷你宠物面板显示你现在所拥有的所有迷你宠物。
你可以在此处查看有关迷你宠物和作者的详细信息。

迷你宠物是桌宠角色的宠物 (套娃)。他们以物品形式存在于背包中, 可以被购买、召唤。
一些迷你宠物会用自己的方式时刻跟随着桌宠角色。

点击 添加迷你宠物, 你可以从选择的文件夹中导入新的迷你宠物。
如果你在寻找新的宠物, 你可以点击面板的超链接按钮查看我们收集到的所有宠物。

绝大多数情况, 应用可以帮你自动导入。但如果你想要手动导入：</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="325"/>
        <source>1. Prepare/download the Mini-Pet folder containing all files
2. Copy the folder to App resource folder (you can click 'Go to Folder' button);
3. Close App and open again;
4. You will see the Mini-Pet show up here;
5. If App crushed when calling the Mini-Pet, it means the source file is problematic, please contact the author for help.</source>
        <translation>1. 准备/下载 包含所有文件的宠物文件夹；
2. 将文件夹复制到应用程序资源文件夹（您可以点击“前往文件夹”按钮）；
3. 重启应用程序；
4. 您会在此处看到该宠物；
5. 如果在召唤该宠物时应用程序崩溃，表示文件有问题，请联系作者以获取帮助。</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="350"/>
        <source>OK</source>
        <translation>确定</translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/PetCardUI.py" line="353"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>BubbleManager</name>
    <message>
        <source>USERTAG I want to have ITEMNAME</source>
        <translation>USERTAG 我想要 ITEMNAME</translation>
    </message>
    <message>
        <source>USERTAG You should be focusing on your work</source>
        <translation>USERTAG 你现在应该专注在任务上</translation>
    </message>
</context>
</TS>

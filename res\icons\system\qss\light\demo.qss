MinimizeButton {
    qproperty-normalColor: black;
    qproperty-normalBackgroundColor: transparent;
    qproperty-hoverColor: black;
    qproperty-hoverBackgroundColor: rgba(0, 0, 0, 26);
    qproperty-pressedColor: black;
    qproperty-pressedBackgroundColor: rgba(0, 0, 0, 51)
}


MaximizeButton {
    qproperty-normalColor: black;
    qproperty-normalBackgroundColor: transparent;
    qproperty-hoverColor: black;
    qproperty-hoverBackgroundColor: rgba(0, 0, 0, 26);
    qproperty-pressedColor: black;
    qproperty-pressedBackgroundColor: rgba(0, 0, 0, 51)
}

CloseButton {
    qproperty-normalColor: black;
    qproperty-normalBackgroundColor: transparent;
}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI聊天功能测试脚本
用于验证AI对话功能的各个组件是否正常工作
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

def test_ai_service():
    """测试AI服务模块"""
    print("=== 测试AI服务模块 ===")
    try:
        from DyberPet.ai_service import get_ai_response
        print("✓ AI服务模块导入成功")
        
        # 测试无API Key的情况
        status, response = get_ai_response("", "派蒙", "你好")
        if status == "error" and "API Key" in response:
            print("✓ API Key验证正常")
        else:
            print("✗ API Key验证异常")
            
        print("AI服务模块测试完成\n")
        return True
    except Exception as e:
        print(f"✗ AI服务模块测试失败: {e}\n")
        return False

def test_chat_window():
    """测试聊天窗口"""
    print("=== 测试聊天窗口 ===")
    try:
        # 只测试导入，不创建实例
        import importlib.util
        spec = importlib.util.find_spec("DyberPet.extra_windows")
        if spec is not None:
            print("✓ extra_windows模块存在")

            # 检查ChatWindow类是否在模块中定义
            with open("DyberPet/extra_windows.py", "r", encoding="utf-8") as f:
                content = f.read()
                if "class ChatWindow" in content:
                    print("✓ ChatWindow类已定义")
                    if "send_message = Signal" in content:
                        print("✓ 发送信号已定义")
                    if "input_box" in content:
                        print("✓ 输入框代码存在")
                    if "send_button" in content:
                        print("✓ 发送按钮代码存在")
                else:
                    print("✗ ChatWindow类未定义")
                    return False
        else:
            print("✗ extra_windows模块不存在")
            return False

        print("聊天窗口测试完成\n")
        return True
    except Exception as e:
        print(f"✗ 聊天窗口测试失败: {e}\n")
        return False

def test_settings():
    """测试设置模块"""
    print("=== 测试设置模块 ===")
    try:
        # 检查设置文件是否存在API Key配置
        import os
        settings_file = "data/settings.json"
        if os.path.exists(settings_file):
            print("✓ 设置文件存在")
            import json
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings_data = json.load(f)
            if 'api_key' in settings_data:
                print("✓ API Key设置存在")
                print(f"  当前API Key: {'已设置' if settings_data['api_key'] else '未设置'}")
            else:
                print("✗ API Key设置不存在")
        else:
            print("✗ 设置文件不存在")

        print("设置模块测试完成\n")
        return True
    except Exception as e:
        print(f"✗ 设置模块测试失败: {e}\n")
        return False

def test_bubble_config():
    """测试气泡配置"""
    print("=== 测试气泡配置 ===")
    try:
        import json
        with open('res/icons/bubble_conf.json', 'r', encoding='utf-8') as f:
            bubble_config = json.load(f)
        
        print("✓ 气泡配置文件读取成功")
        
        if 'chat' in bubble_config:
            print("✓ chat类型气泡配置存在")
        else:
            print("✗ chat类型气泡配置不存在")
            
        if 'system' in bubble_config:
            print("✓ system类型气泡配置存在")
        else:
            print("✗ system类型气泡配置不存在")
            
        print("气泡配置测试完成\n")
        return True
    except Exception as e:
        print(f"✗ 气泡配置测试失败: {e}\n")
        return False

def main():
    """主测试函数"""
    print("开始AI对话功能测试...\n")
    
    tests = [
        test_ai_service,
        test_chat_window,
        test_settings,
        test_bubble_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！AI对话功能应该可以正常工作。")
        print("\n使用说明:")
        print("1. 在设置中配置API Key")
        print("2. 右键点击宠物，选择'Chat'")
        print("3. 在弹出的对话框中输入消息")
        print("4. 按回车或点击发送按钮")
        print("5. AI回复将以气泡形式显示在宠物上方")
    else:
        print("❌ 部分测试失败，请检查相关组件。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
